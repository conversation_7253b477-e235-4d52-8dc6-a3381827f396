.386
.model flat, stdcall
option casemap:none

include \masm32\include\windows.inc
include \masm32\include\kernel32.inc
include \masm32\include\user32.inc
include \masm32\include\msvcrt.inc
include \masm32\include\advapi32.inc

includelib \masm32\lib\kernel32.lib
includelib \masm32\lib\user32.lib
includelib \masm32\lib\msvcrt.lib
includelib \masm32\lib\advapi32.lib

; تعريفات Windows Crypto API
PROV_RSA_AES            equ 24
CRYPT_VERIFYCONTEXT     equ 0F0000000h
CALG_AES_256            equ 00006610h
CALG_SHA_256            equ 0000800Ch
CRYPT_EXPORTABLE        equ 00000001h
CRYPT_ENCRYPT           equ 00000001h

.data
    ; رسائل النظام
    title_msg       db "🔐 Advanced EXE Wrapper Tool v2.0", 0
    usage_msg       db "الاستخدام: wrapper_tool.exe <ملف_exe> <كلمة_المرور> <ملف_الإخراج>", 0
    file_not_found  db "لم يتم العثور على الملف المحدد!", 0
    success_msg     db "تم تغليف الملف بنجاح باستخدام تشفير AES-256!", 0
    error_msg       db "حدث خطأ أثناء التغليف!", 0
    crypto_error    db "خطأ في نظام التشفير المتقدم!", 0

    ; متغيرات العمل
    input_filename  db 256 dup(0)
    output_filename db 256 dup(0)
    password_buffer db 256 dup(0)
    file_buffer     db 2000000 dup(0)  ; مخزن أكبر للملف

    file_size       dd 0
    encrypted_size  dd 0
    bytes_read      dd 0
    bytes_written   dd 0
    input_handle    dd 0
    output_handle   dd 0

    ; قالب الملف المحمي المتقدم
    protected_header db "AES256_PROTECTED_EXE_V2", 0
    header_size     dd 24

    ; متغيرات التشفير
    hCryptProv      dd 0
    hHash           dd 0
    hKey            dd 0
    password_hash   db 32 dup(0)
    dwDataLen       dd 0

.code

; دالة إنشاء مفتاح AES من كلمة المرور
create_encryption_key proc password_ptr:DWORD
    push ebp
    mov ebp, esp

    ; الحصول على مزود التشفير
    invoke CryptAcquireContext, offset hCryptProv, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT
    cmp eax, 0
    je key_error

    ; إنشاء hash object
    invoke CryptCreateHash, hCryptProv, CALG_SHA_256, 0, 0, offset hHash
    cmp eax, 0
    je key_error

    ; hash كلمة المرور
    invoke lstrlen, password_ptr
    invoke CryptHashData, hHash, password_ptr, eax, 0
    cmp eax, 0
    je key_error

    ; الحصول على hash value
    mov dwDataLen, 32
    invoke CryptGetHashParam, hHash, 2, offset password_hash, offset dwDataLen, 0
    cmp eax, 0
    je key_error

    ; إنشاء مفتاح AES
    invoke CryptDeriveKey, hCryptProv, CALG_AES_256, hHash, CRYPT_EXPORTABLE, offset hKey
    cmp eax, 0
    je key_error

    mov eax, 1  ; نجح
    jmp key_done

key_error:
    xor eax, eax  ; فشل

key_done:
    ; تنظيف hash
    cmp hHash, 0
    je skip_hash_cleanup
    invoke CryptDestroyHash, hHash
    mov hHash, 0
skip_hash_cleanup:

    mov esp, ebp
    pop ebp
    ret
create_encryption_key endp

; دالة تشفير البيانات باستخدام AES
encrypt_file_data proc data_ptr:DWORD, data_size:DWORD
    push ebp
    mov ebp, esp
    local temp_size:DWORD

    ; نسخ حجم البيانات
    mov eax, data_size
    mov temp_size, eax

    ; تشفير البيانات
    invoke CryptEncrypt, hKey, 0, TRUE, 0, data_ptr, offset temp_size, data_size
    cmp eax, 0
    je encrypt_error

    ; إرجاع الحجم الجديد
    mov eax, temp_size
    jmp encrypt_done

encrypt_error:
    xor eax, eax  ; فشل

encrypt_done:
    mov esp, ebp
    pop ebp
    ret
encrypt_file_data endp

; دالة تنظيف موارد التشفير
cleanup_encryption proc
    cmp hKey, 0
    je skip_key_cleanup
    invoke CryptDestroyKey, hKey
    mov hKey, 0
skip_key_cleanup:

    cmp hCryptProv, 0
    je skip_prov_cleanup
    invoke CryptReleaseContext, hCryptProv, 0
    mov hCryptProv, 0
skip_prov_cleanup:

    ret
cleanup_encryption endp

; دالة قراءة الملف
read_input_file proc filename_ptr:DWORD
    ; فتح الملف للقراءة
    invoke CreateFile, filename_ptr, GENERIC_READ, FILE_SHARE_READ, NULL, \
           OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL
    mov input_handle, eax
    cmp eax, INVALID_HANDLE_VALUE
    je read_error
    
    ; الحصول على حجم الملف
    invoke GetFileSize, input_handle, NULL
    mov file_size, eax
    cmp eax, 1000000  ; فحص الحد الأقصى
    jg read_error
    
    ; قراءة الملف
    invoke ReadFile, input_handle, offset file_buffer, file_size, \
           offset bytes_read, NULL
    
    ; إغلاق الملف
    invoke CloseHandle, input_handle
    
    mov eax, 1  ; نجح
    ret
    
read_error:
    cmp input_handle, INVALID_HANDLE_VALUE
    je skip_close
    invoke CloseHandle, input_handle
skip_close:
    xor eax, eax  ; فشل
    ret
read_input_file endp

; دالة إنشاء الملف المحمي المتقدم
create_protected_file proc output_filename_ptr:DWORD
    push ebp
    mov ebp, esp

    ; إنشاء مفتاح التشفير
    invoke create_encryption_key, offset password_buffer
    cmp eax, 0
    je create_crypto_error

    ; تشفير بيانات الملف
    invoke encrypt_file_data, offset file_buffer, file_size
    cmp eax, 0
    je create_crypto_error
    mov encrypted_size, eax

    ; إنشاء الملف الجديد
    invoke CreateFile, output_filename_ptr, GENERIC_WRITE, 0, NULL, \
           CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL
    mov output_handle, eax
    cmp eax, INVALID_HANDLE_VALUE
    je create_error

    ; كتابة رأس الملف المحمي المتقدم
    invoke WriteFile, output_handle, offset protected_header, header_size, \
           offset bytes_written, NULL
    cmp eax, 0
    je create_error

    ; كتابة حجم الملف الأصلي
    invoke WriteFile, output_handle, offset file_size, 4, \
           offset bytes_written, NULL
    cmp eax, 0
    je create_error

    ; كتابة حجم الملف المشفر
    invoke WriteFile, output_handle, offset encrypted_size, 4, \
           offset bytes_written, NULL
    cmp eax, 0
    je create_error

    ; كتابة hash كلمة المرور
    invoke WriteFile, output_handle, offset password_hash, 32, \
           offset bytes_written, NULL
    cmp eax, 0
    je create_error

    ; كتابة البيانات المشفرة
    invoke WriteFile, output_handle, offset file_buffer, encrypted_size, \
           offset bytes_written, NULL
    cmp eax, 0
    je create_error

    ; إغلاق الملف
    invoke CloseHandle, output_handle
    mov output_handle, 0

    ; تنظيف موارد التشفير
    invoke cleanup_encryption

    mov eax, 1  ; نجح
    jmp create_done

create_crypto_error:
    invoke MessageBox, NULL, offset crypto_error, offset title_msg, MB_OK
    invoke cleanup_encryption
    xor eax, eax  ; فشل
    jmp create_done

create_error:
    cmp output_handle, 0
    je skip_close2
    invoke CloseHandle, output_handle
    mov output_handle, 0
skip_close2:
    invoke cleanup_encryption
    xor eax, eax  ; فشل

create_done:
    mov esp, ebp
    pop ebp
    ret
create_protected_file endp

; دالة تحليل معاملات سطر الأوامر
parse_command_line proc
    ; هذه دالة مبسطة - في التطبيق الحقيقي يجب تحليل argc/argv
    ; للبساطة، سنستخدم قيم افتراضية للاختبار
    
    ; نسخ اسم الملف المدخل
    mov esi, offset input_filename
    mov byte ptr [esi], 't'
    mov byte ptr [esi+1], 'e'
    mov byte ptr [esi+2], 's'
    mov byte ptr [esi+3], 't'
    mov byte ptr [esi+4], '.'
    mov byte ptr [esi+5], 'e'
    mov byte ptr [esi+6], 'x'
    mov byte ptr [esi+7], 'e'
    mov byte ptr [esi+8], 0
    
    ; نسخ كلمة المرور
    mov esi, offset password_buffer
    mov byte ptr [esi], 'm'
    mov byte ptr [esi+1], 'y'
    mov byte ptr [esi+2], 'p'
    mov byte ptr [esi+3], 'a'
    mov byte ptr [esi+4], 's'
    mov byte ptr [esi+5], 's'
    mov byte ptr [esi+6], 0
    
    ; نسخ اسم ملف الإخراج
    mov esi, offset output_filename
    mov byte ptr [esi], 'p'
    mov byte ptr [esi+1], 'r'
    mov byte ptr [esi+2], 'o'
    mov byte ptr [esi+3], 't'
    mov byte ptr [esi+4], 'e'
    mov byte ptr [esi+5], 'c'
    mov byte ptr [esi+6], 't'
    mov byte ptr [esi+7], 'e'
    mov byte ptr [esi+8], 'd'
    mov byte ptr [esi+9], '.'
    mov byte ptr [esi+10], 'e'
    mov byte ptr [esi+11], 'x'
    mov byte ptr [esi+12], 'e'
    mov byte ptr [esi+13], 0
    
    ret
parse_command_line endp

start:
    ; تحليل معاملات سطر الأوامر
    invoke parse_command_line
    
    ; قراءة الملف المدخل
    invoke read_input_file, offset input_filename
    cmp eax, 0
    je file_error
    
    ; إنشاء الملف المحمي
    invoke create_protected_file, offset output_filename
    cmp eax, 0
    je create_file_error
    
    ; رسالة النجاح
    invoke MessageBox, NULL, offset success_msg, offset title_msg, MB_OK
    jmp exit_program
    
file_error:
    invoke MessageBox, NULL, offset file_not_found, offset title_msg, MB_OK
    jmp exit_program
    
create_file_error:
    invoke MessageBox, NULL, offset error_msg, offset title_msg, MB_OK
    
exit_program:
    invoke ExitProcess, 0

end start
