.386
.model flat, stdcall
option casemap:none

include \masm32\include\windows.inc
include \masm32\include\kernel32.inc
include \masm32\include\user32.inc
include \masm32\include\msvcrt.inc

includelib \masm32\lib\kernel32.lib
includelib \masm32\lib\user32.lib
includelib \masm32\lib\msvcrt.lib

.data
    ; رسائل النظام
    title_msg       db "🔐 EXE Wrapper Tool", 0
    usage_msg       db "الاستخدام: wrapper_tool.exe <ملف_exe> <كلمة_المرور> <ملف_الإخراج>", 0
    file_not_found  db "لم يتم العثور على الملف المحدد!", 0
    success_msg     db "تم تغليف الملف بنجاح!", 0
    error_msg       db "حدث خطأ أثناء التغليف!", 0
    
    ; متغيرات العمل
    input_filename  db 256 dup(0)
    output_filename db 256 dup(0)
    password_buffer db 256 dup(0)
    file_buffer     db 1000000 dup(0)  ; مخزن مؤقت للملف
    
    file_size       dd 0
    bytes_read      dd 0
    bytes_written   dd 0
    input_handle    dd 0
    output_handle   dd 0
    
    ; قالب الملف المحمي
    protected_template db "PROTECTED_EXE_HEADER", 0
    template_size   dd 18

.code

; دالة تشفير البيانات
encrypt_data proc password_ptr:DWORD, data_ptr:DWORD, data_size:DWORD
    push esi
    push edi
    push ecx
    push edx
    
    mov esi, password_ptr
    mov edi, data_ptr
    mov ecx, data_size
    xor edx, edx
    
encrypt_loop:
    cmp ecx, 0
    je encrypt_done
    
    ; احصل على حرف من كلمة المرور
    mov al, byte ptr [esi + edx]
    cmp al, 0
    jne use_char
    xor edx, edx  ; ابدأ من جديد
    mov al, byte ptr [esi]
    
use_char:
    ; تشفير XOR
    xor byte ptr [edi], al
    inc edi
    inc edx
    dec ecx
    jmp encrypt_loop
    
encrypt_done:
    pop edx
    pop ecx
    pop edi
    pop esi
    ret
encrypt_data endp

; دالة قراءة الملف
read_input_file proc filename_ptr:DWORD
    ; فتح الملف للقراءة
    invoke CreateFile, filename_ptr, GENERIC_READ, FILE_SHARE_READ, NULL, \
           OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL
    mov input_handle, eax
    cmp eax, INVALID_HANDLE_VALUE
    je read_error
    
    ; الحصول على حجم الملف
    invoke GetFileSize, input_handle, NULL
    mov file_size, eax
    cmp eax, 1000000  ; فحص الحد الأقصى
    jg read_error
    
    ; قراءة الملف
    invoke ReadFile, input_handle, offset file_buffer, file_size, \
           offset bytes_read, NULL
    
    ; إغلاق الملف
    invoke CloseHandle, input_handle
    
    mov eax, 1  ; نجح
    ret
    
read_error:
    cmp input_handle, INVALID_HANDLE_VALUE
    je skip_close
    invoke CloseHandle, input_handle
skip_close:
    xor eax, eax  ; فشل
    ret
read_input_file endp

; دالة إنشاء الملف المحمي
create_protected_file proc output_filename_ptr:DWORD
    ; إنشاء الملف الجديد
    invoke CreateFile, output_filename_ptr, GENERIC_WRITE, 0, NULL, \
           CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL
    mov output_handle, eax
    cmp eax, INVALID_HANDLE_VALUE
    je create_error
    
    ; كتابة رأس الملف المحمي
    invoke WriteFile, output_handle, offset protected_template, template_size, \
           offset bytes_written, NULL
    
    ; كتابة حجم الملف الأصلي
    invoke WriteFile, output_handle, offset file_size, 4, \
           offset bytes_written, NULL
    
    ; كتابة كلمة المرور المشفرة
    invoke encrypt_data, offset password_buffer, offset password_buffer, 32
    invoke WriteFile, output_handle, offset password_buffer, 32, \
           offset bytes_written, NULL
    
    ; تشفير وكتابة بيانات الملف
    invoke encrypt_data, offset password_buffer, offset file_buffer, file_size
    invoke WriteFile, output_handle, offset file_buffer, file_size, \
           offset bytes_written, NULL
    
    ; إغلاق الملف
    invoke CloseHandle, output_handle
    
    mov eax, 1  ; نجح
    ret
    
create_error:
    cmp output_handle, INVALID_HANDLE_VALUE
    je skip_close2
    invoke CloseHandle, output_handle
skip_close2:
    xor eax, eax  ; فشل
    ret
create_protected_file endp

; دالة تحليل معاملات سطر الأوامر
parse_command_line proc
    ; هذه دالة مبسطة - في التطبيق الحقيقي يجب تحليل argc/argv
    ; للبساطة، سنستخدم قيم افتراضية للاختبار
    
    ; نسخ اسم الملف المدخل
    mov esi, offset input_filename
    mov byte ptr [esi], 't'
    mov byte ptr [esi+1], 'e'
    mov byte ptr [esi+2], 's'
    mov byte ptr [esi+3], 't'
    mov byte ptr [esi+4], '.'
    mov byte ptr [esi+5], 'e'
    mov byte ptr [esi+6], 'x'
    mov byte ptr [esi+7], 'e'
    mov byte ptr [esi+8], 0
    
    ; نسخ كلمة المرور
    mov esi, offset password_buffer
    mov byte ptr [esi], 'm'
    mov byte ptr [esi+1], 'y'
    mov byte ptr [esi+2], 'p'
    mov byte ptr [esi+3], 'a'
    mov byte ptr [esi+4], 's'
    mov byte ptr [esi+5], 's'
    mov byte ptr [esi+6], 0
    
    ; نسخ اسم ملف الإخراج
    mov esi, offset output_filename
    mov byte ptr [esi], 'p'
    mov byte ptr [esi+1], 'r'
    mov byte ptr [esi+2], 'o'
    mov byte ptr [esi+3], 't'
    mov byte ptr [esi+4], 'e'
    mov byte ptr [esi+5], 'c'
    mov byte ptr [esi+6], 't'
    mov byte ptr [esi+7], 'e'
    mov byte ptr [esi+8], 'd'
    mov byte ptr [esi+9], '.'
    mov byte ptr [esi+10], 'e'
    mov byte ptr [esi+11], 'x'
    mov byte ptr [esi+12], 'e'
    mov byte ptr [esi+13], 0
    
    ret
parse_command_line endp

start:
    ; تحليل معاملات سطر الأوامر
    invoke parse_command_line
    
    ; قراءة الملف المدخل
    invoke read_input_file, offset input_filename
    cmp eax, 0
    je file_error
    
    ; إنشاء الملف المحمي
    invoke create_protected_file, offset output_filename
    cmp eax, 0
    je create_file_error
    
    ; رسالة النجاح
    invoke MessageBox, NULL, offset success_msg, offset title_msg, MB_OK
    jmp exit_program
    
file_error:
    invoke MessageBox, NULL, offset file_not_found, offset title_msg, MB_OK
    jmp exit_program
    
create_file_error:
    invoke MessageBox, NULL, offset error_msg, offset title_msg, MB_OK
    
exit_program:
    invoke ExitProcess, 0

end start
