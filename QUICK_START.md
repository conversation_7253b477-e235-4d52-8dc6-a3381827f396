# 🚀 دليل البدء السريع - نظام حماية الملفات التنفيذية

## الخطوات السريعة

### 1️⃣ التحضير
```bash
# تأكد من تثبيت MASM32 في C:\masm32
# إذا لم يكن مثبتاً، حمله من: http://www.masm32.com
```

### 2️⃣ التجميع
```bash
# تجميع جميع المكونات
build.bat

# تجميع برنامج الاختبار
build_test.bat
```

### 3️⃣ الاختبار السريع
```bash
# 1. تشغيل أداة التغليف (ستستخدم قيم افتراضية للاختبار)
wrapper_tool.exe

# 2. تشغيل الملف المحمي الناتج
protected.exe

# 3. أدخل كلمة المرور: test
```

## الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `exe_locker.exe` | البرنامج الرئيسي للحماية |
| `wrapper_tool.exe` | أداة تغليف الملفات |
| `password_dialog.exe` | نافذة إدخال كلمة المرور |
| `test_program.exe` | برنامج اختبار |

## مثال عملي

### حماية ملف حقيقي
```bash
# 1. ضع الملف المراد حمايته في نفس المجلد
copy "C:\path\to\your\program.exe" input.exe

# 2. قم بتعديل wrapper_tool.asm لاستخدام الملف الحقيقي
# أو استخدم القيم الافتراضية للاختبار

# 3. أعد التجميع
build.bat

# 4. شغل أداة التغليف
wrapper_tool.exe

# 5. شغل الملف المحمي
protected.exe
```

## كلمات المرور الافتراضية

للاختبار السريع، الكلمات الافتراضية هي:
- **wrapper_tool**: `mypass`
- **exe_locker**: `test`

## استكشاف الأخطاء السريع

### ❌ خطأ في التجميع
```bash
# تحقق من مسار MASM32
dir C:\masm32\bin\ml.exe

# إذا لم يوجد، أعد تثبيت MASM32
```

### ❌ الملف لا يعمل
```bash
# تحقق من صلاحيات التشغيل
# تأكد من أن Windows Defender لا يحجب الملف
# شغل كمدير إذا لزم الأمر
```

### ❌ كلمة مرور خاطئة
```bash
# استخدم كلمة المرور الافتراضية: test
# تأكد من عدم وجود مسافات إضافية
```

## نصائح سريعة

### 🔧 التخصيص
- عدل كلمة المرور في `wrapper_tool.asm`
- غير الرسائل في ملفات `.asm`
- أضف المزيد من التشفير حسب الحاجة

### 🛡️ الأمان
- استخدم كلمات مرور قوية في الإنتاج
- احتفظ بنسخة احتياطية من الملفات الأصلية
- اختبر على ملفات غير مهمة أولاً

### 📊 الأداء
- الملفات الكبيرة قد تستغرق وقتاً أطول
- تأكد من وجود مساحة كافية على القرص
- أغلق برامج مكافحة الفيروسات مؤقتاً إذا لزم الأمر

## الخطوات التالية

بعد نجاح الاختبار الأولي:

1. **تخصيص النظام** حسب احتياجاتك
2. **تحسين الأمان** بإضافة تشفير أقوى
3. **تطوير واجهة المستخدم** لسهولة الاستخدام
4. **إضافة ميزات متقدمة** مثل انتهاء الصلاحية

## الدعم

إذا واجهت مشاكل:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. تحقق من رسائل الخطأ في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات

---
**تذكر**: هذا النظام للأغراض التعليمية والاختبار. استخدمه بمسؤولية! 🔐
