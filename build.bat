@echo off
echo 🔐 Building Advanced Executable File Locker v2.0...
echo 🛡️ Features: AES-256 Encryption, Windows 7/10/11 Compatible
echo.

REM تحقق من وجود MASM32
if not exist "C:\masm32\bin\ml.exe" (
    echo ❌ خطأ: MASM32 غير مثبت أو غير موجود في C:\masm32
    echo 📥 يرجى تحميل وتثبيت MASM32 من: http://www.masm32.com
    echo.
    echo 💡 نصائح التثبيت:
    echo    - تأكد من التثبيت في المجلد الافتراضي C:\masm32
    echo    - شغل المثبت كمدير إذا لزم الأمر
    echo    - أعد تشغيل Command Prompt بعد التثبيت
    pause
    exit /b 1
)

REM تحقق من إصدار Windows
echo 🔍 فحص توافق النظام...
ver | find "Windows" >nul
if errorlevel 1 (
    echo ⚠️ تحذير: قد لا يعمل النظام على هذا الإصدار من Windows
)

REM إعداد المتغيرات
set MASM32_PATH=C:\masm32
set PATH=%MASM32_PATH%\bin;%PATH%
set INCLUDE=%MASM32_PATH%\include
set LIB=%MASM32_PATH%\lib

echo ✅ تم العثور على MASM32 في: %MASM32_PATH%

echo.
echo 🔧 تجميع المكونات...

echo 📱 تجميع نافذة كلمة المرور...
ml /c /coff /Cp password_dialog.asm
if errorlevel 1 goto error

echo 🛠️ تجميع أداة التغليف المتقدمة...
ml /c /coff /Cp wrapper_tool.asm
if errorlevel 1 goto error

echo 🔐 تجميع برنامج الحماية الرئيسي...
ml /c /coff /Cp exe_locker.asm
if errorlevel 1 goto error

echo.
echo 🔗 ربط الملفات...

echo 📱 ربط نافذة كلمة المرور...
link /SUBSYSTEM:WINDOWS /LARGEADDRESSAWARE password_dialog.obj
if errorlevel 1 goto error

echo 🛠️ ربط أداة التغليف...
link /SUBSYSTEM:CONSOLE /LARGEADDRESSAWARE wrapper_tool.obj
if errorlevel 1 goto error

echo 🔐 ربط برنامج الحماية...
link /SUBSYSTEM:WINDOWS /LARGEADDRESSAWARE exe_locker.obj
if errorlevel 1 goto error

echo.
echo 🎉 تم التجميع بنجاح!
echo.
echo 📦 الملفات المنتجة:
echo   🔐 exe_locker.exe      : برنامج الحماية الرئيسي (AES-256)
echo   🛠️ wrapper_tool.exe    : أداة تغليف الملفات المتقدمة
echo   📱 password_dialog.exe : نافذة إدخال كلمة المرور
echo.

REM تنظيف الملفات المؤقتة
del *.obj 2>nul

echo 🔧 مميزات النسخة الجديدة:
echo   ✅ تشفير AES-256 قوي
echo   ✅ متوافق مع Windows 7/10/11
echo   ✅ حماية متقدمة ضد الهندسة العكسية
echo   ✅ استخدام Windows Crypto API
echo.
echo 🚀 لاختبار النظام:
echo   1. ضع ملف .exe في نفس المجلد
echo   2. شغل wrapper_tool.exe لتغليف الملف
echo   3. شغل الملف المحمي الناتج
echo   4. أدخل كلمة المرور الصحيحة
echo.
echo 💡 نصيحة: ابدأ بـ build_test.bat لإنشاء ملف اختبار
echo.
pause
goto end

:error
echo.
echo ❌ حدث خطأ أثناء التجميع!
echo تحقق من:
echo - تثبيت MASM32 بشكل صحيح
echo - صحة ملفات الكود المصدري
echo - صلاحيات الكتابة في المجلد
echo.
pause

:end
