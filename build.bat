@echo off
echo 🔐 Building Executable File Locker...
echo.

REM تحقق من وجود MASM32
if not exist "C:\masm32\bin\ml.exe" (
    echo خطأ: MASM32 غير مثبت أو غير موجود في C:\masm32
    echo يرجى تحميل وتثبيت MASM32 من: http://www.masm32.com
    pause
    exit /b 1
)

REM إعداد المتغيرات
set MASM32_PATH=C:\masm32
set PATH=%MASM32_PATH%\bin;%PATH%
set INCLUDE=%MASM32_PATH%\include
set LIB=%MASM32_PATH%\lib

echo تجميع نافذة كلمة المرور...
ml /c /coff password_dialog.asm
if errorlevel 1 goto error

echo تجميع أداة التغليف...
ml /c /coff wrapper_tool.asm
if errorlevel 1 goto error

echo تجميع برنامج الحماية الرئيسي...
ml /c /coff exe_locker.asm
if errorlevel 1 goto error

echo ربط نافذة كلمة المرور...
link /SUBSYSTEM:WINDOWS password_dialog.obj
if errorlevel 1 goto error

echo ربط أداة التغليف...
link /SUBSYSTEM:CONSOLE wrapper_tool.obj
if errorlevel 1 goto error

echo ربط برنامج الحماية...
link /SUBSYSTEM:WINDOWS exe_locker.obj
if errorlevel 1 goto error

echo.
echo ✅ تم التجميع بنجاح!
echo.
echo الملفات المنتجة:
echo - password_dialog.exe : نافذة إدخال كلمة المرور
echo - wrapper_tool.exe    : أداة تغليف الملفات
echo - exe_locker.exe      : برنامج الحماية الرئيسي
echo.

REM تنظيف الملفات المؤقتة
del *.obj 2>nul

echo لاختبار النظام:
echo 1. ضع ملف .exe في نفس المجلد
echo 2. شغل wrapper_tool.exe لتغليف الملف
echo 3. شغل الملف المحمي الناتج
echo.
pause
goto end

:error
echo.
echo ❌ حدث خطأ أثناء التجميع!
echo تحقق من:
echo - تثبيت MASM32 بشكل صحيح
echo - صحة ملفات الكود المصدري
echo - صلاحيات الكتابة في المجلد
echo.
pause

:end
