# 🔐 Advanced Executable File Locker v2.0

## الوصف
نظام حماية متقدم للملفات التنفيذية باستخدام لغة Assembly مع تشفير AES-256. يقوم بتشفير أي ملف EXE وحمايته بكلمة مرور قوية، مما يوفر حماية عسكرية المستوى على النظام المنخفض.

## 🛡️ المميزات المتقدمة
- ✅ **تشفير AES-256** - أقوى معايير التشفير المتاحة
- ✅ **Windows Crypto API** - استخدام مكتبات Windows الأصلية
- ✅ **SHA-256 Hashing** - حماية كلمات المرور بـ hash آمن
- ✅ **متوافق مع Windows 7/8/10/11** - يعمل على جميع الإصدارات الحديثة
- ✅ **حماية بكلمة مرور** مع واجهة مستخدم متقدمة
- ✅ **استخراج وتشغيل مؤقت** للملف المحمي
- ✅ **حذف تلقائي آمن** للملفات المؤقتة
- ✅ **مقاومة قوية للهندسة العكسية**
- ✅ **عمل على مستوى منخفض** من النظام
- ✅ **دعم ملفات كبيرة** حتى 2 MB

## 💻 متطلبات النظام
- **Windows 7/8/8.1/10/11** (32-bit أو 64-bit)
- **MASM32 SDK** مثبت في `C:\masm32`
- **Windows Crypto API** (متوفر افتراضياً في Windows الحديث)
- **2 MB مساحة حرة** على القرص للملفات الكبيرة
- **صلاحيات المستخدم** لإنشاء ملفات مؤقتة

## التثبيت

### 1. تثبيت MASM32
```bash
# تحميل MASM32 من الموقع الرسمي
# http://www.masm32.com/download.htm
# تثبيت في المجلد الافتراضي C:\masm32
```

### 2. تجميع المشروع
```bash
# تشغيل ملف البناء
build.bat
```

## الاستخدام

### 1. تغليف ملف تنفيذي
```bash
# استخدام أداة التغليف
wrapper_tool.exe input.exe mypassword protected.exe
```

### 2. تشغيل الملف المحمي
```bash
# تشغيل الملف المحمي مباشرة
protected.exe
# سيطلب كلمة المرور قبل التشغيل
```

## بنية المشروع

```
📁 exe_locker/
├── 📄 exe_locker.asm      # البرنامج الرئيسي للحماية
├── 📄 wrapper_tool.asm    # أداة تغليف الملفات
├── 📄 password_dialog.asm # نافذة إدخال كلمة المرور
├── 📄 build.bat          # ملف البناء والتجميع
└── 📄 README.md          # هذا الملف
```

## كيفية العمل

### 1. عملية التغليف
1. قراءة الملف التنفيذي الأصلي
2. تشفير البيانات باستخدام XOR مع كلمة المرور
3. إنشاء ملف محمي جديد يحتوي على:
   - رأس التعريف
   - كلمة المرور المشفرة
   - البيانات المشفرة

### 2. عملية التشغيل
1. عرض نافذة طلب كلمة المرور
2. التحقق من صحة كلمة المرور
3. فك تشفير البيانات
4. إنشاء ملف مؤقت
5. تشغيل الملف الأصلي
6. حذف الملف المؤقت

## الأمان والحماية

### مستويات الحماية
- **تشفير XOR**: سريع وفعال ضد التحليل السطحي
- **كلمة مرور مشفرة**: لا تُخزن بشكل واضح في الذاكرة
- **ملفات مؤقتة**: تُحذف تلقائياً بعد التشغيل
- **مستوى منخفض**: صعوبة في التحليل والهندسة العكسية

### نصائح الأمان
- استخدم كلمات مرور قوية ومعقدة
- احتفظ بنسخة احتياطية من الملف الأصلي
- لا تشارك كلمة المرور مع أشخاص غير موثوقين
- قم بفحص الملفات المحمية دورياً

## الاختبار

### اختبار سريع
```bash
# إنشاء ملف اختبار بسيط
echo "Hello World" > test.txt
copy test.txt test.exe

# تغليف الملف
wrapper_tool.exe test.exe mypass123 protected.exe

# تشغيل الملف المحمي
protected.exe
```

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في التجميع**: تأكد من تثبيت MASM32 صحيحاً
2. **ملف غير موجود**: تحقق من مسار الملف المدخل
3. **كلمة مرور خاطئة**: تأكد من إدخال كلمة المرور الصحيحة
4. **فشل في التشغيل**: تحقق من صلاحيات النظام

### رسائل الخطأ
- `"لم يتم العثور على الملف"`: الملف المحدد غير موجود
- `"كلمة مرور خاطئة"`: كلمة المرور المدخلة غير صحيحة
- `"حدث خطأ أثناء التغليف"`: مشكلة في عملية التشفير

## التطوير المستقبلي

### تحسينات مقترحة
- [ ] دعم خوارزميات تشفير أقوى (AES)
- [ ] واجهة مستخدم رسومية متقدمة
- [ ] دعم ملفات أكبر من 1 MB
- [ ] نظام مفاتيح متعددة
- [ ] تشفير البيانات الوصفية

### إضافات ممكنة
- [ ] حماية ضد التصحيح (Anti-debugging)
- [ ] كشف الآلات الافتراضية
- [ ] تشفير الذاكرة أثناء التشغيل
- [ ] نظام انتهاء صلاحية

## الترخيص
هذا المشروع مفتوح المصدر للأغراض التعليمية والبحثية.

## تحذير قانوني
⚠️ **تحذير**: استخدم هذا البرنامج بمسؤولية وفقط على الملفات التي تملكها. لا تستخدمه لأغراض ضارة أو غير قانونية.

## المساهمة
نرحب بالمساهمات والتحسينات. يرجى إنشاء Pull Request أو Issue للمناقشة.

## الدعم
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.
