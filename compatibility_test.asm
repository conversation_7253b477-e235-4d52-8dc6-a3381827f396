.386
.model flat, stdcall
option casemap:none

include \masm32\include\windows.inc
include \masm32\include\kernel32.inc
include \masm32\include\user32.inc
include \masm32\include\advapi32.inc

includelib \masm32\lib\kernel32.lib
includelib \masm32\lib\user32.lib
includelib \masm32\lib\advapi32.lib

; تعريفات Windows Crypto API
PROV_RSA_AES            equ 24
CRYPT_VERIFYCONTEXT     equ 0F0000000h
CALG_AES_256            equ 00006610h
CALG_SHA_256            equ 0000800Ch

.data
    title_msg       db "🔍 اختبار التوافق - نظام الحماية المتقدم", 0
    test_start      db "بدء اختبار التوافق مع النظام...", 0
    test_crypto     db "اختبار دعم التشفير المتقدم...", 0
    test_success    db "✅ جميع الاختبارات نجحت!", 13, 10, 13, 10, \
                       "النظام متوافق مع:", 13, 10, \
                       "🔹 Windows Crypto API", 13, 10, \
                       "🔹 تشفير AES-256", 13, 10, \
                       "🔹 SHA-256 Hashing", 13, 10, \
                       "🔹 Windows 7/8/10/11", 13, 10, 13, 10, \
                       "يمكنك الآن استخدام النظام بأمان!", 0
    
    test_fail       db "❌ فشل في اختبار التوافق!", 13, 10, 13, 10, \
                       "المشاكل المحتملة:", 13, 10, \
                       "🔸 إصدار Windows غير مدعوم", 13, 10, \
                       "🔸 Windows Crypto API غير متوفر", 13, 10, \
                       "🔸 صلاحيات النظام محدودة", 13, 10, 13, 10, \
                       "يرجى تحديث النظام أو تشغيل البرنامج كمدير.", 0
    
    crypto_fail     db "❌ فشل في اختبار التشفير!", 13, 10, \
                       "تأكد من دعم النظام لـ AES-256.", 0
    
    os_info         db "معلومات النظام:", 13, 10, 0
    
    ; متغيرات الاختبار
    hCryptProv      dd 0
    hHash           dd 0
    hKey            dd 0
    test_data       db "Test Data for Encryption", 0
    test_password   db "TestPassword123", 0
    hash_buffer     db 32 dup(0)
    dwDataLen       dd 0

.code

; دالة اختبار دعم التشفير
test_crypto_support proc
    push ebp
    mov ebp, esp
    
    ; اختبار الحصول على مزود التشفير
    invoke CryptAcquireContext, offset hCryptProv, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT
    cmp eax, 0
    je crypto_test_fail
    
    ; اختبار إنشاء hash
    invoke CryptCreateHash, hCryptProv, CALG_SHA_256, 0, 0, offset hHash
    cmp eax, 0
    je crypto_test_fail
    
    ; اختبار hash البيانات
    invoke lstrlen, offset test_password
    invoke CryptHashData, hHash, offset test_password, eax, 0
    cmp eax, 0
    je crypto_test_fail
    
    ; اختبار الحصول على hash value
    mov dwDataLen, 32
    invoke CryptGetHashParam, hHash, 2, offset hash_buffer, offset dwDataLen, 0
    cmp eax, 0
    je crypto_test_fail
    
    ; اختبار إنشاء مفتاح AES
    invoke CryptDeriveKey, hCryptProv, CALG_AES_256, hHash, 0, offset hKey
    cmp eax, 0
    je crypto_test_fail
    
    ; تنظيف
    invoke CryptDestroyKey, hKey
    invoke CryptDestroyHash, hHash
    invoke CryptReleaseContext, hCryptProv, 0
    
    mov eax, 1  ; نجح
    jmp crypto_test_done
    
crypto_test_fail:
    ; تنظيف في حالة الفشل
    cmp hKey, 0
    je skip_key
    invoke CryptDestroyKey, hKey
skip_key:
    cmp hHash, 0
    je skip_hash
    invoke CryptDestroyHash, hHash
skip_hash:
    cmp hCryptProv, 0
    je skip_prov
    invoke CryptReleaseContext, hCryptProv, 0
skip_prov:
    
    xor eax, eax  ; فشل
    
crypto_test_done:
    mov esp, ebp
    pop ebp
    ret
test_crypto_support endp

; دالة الحصول على معلومات النظام
get_system_info proc
    push ebp
    mov ebp, esp
    local version_info:OSVERSIONINFO
    local info_text[512]:BYTE
    
    ; إعداد هيكل معلومات النسخة
    mov version_info.dwOSVersionInfoSize, sizeof OSVERSIONINFO
    
    ; الحصول على معلومات النسخة
    invoke GetVersionEx, addr version_info
    cmp eax, 0
    je skip_version_info
    
    ; تحديد نوع النظام
    mov eax, version_info.dwMajorVersion
    cmp eax, 6
    je check_minor_6
    cmp eax, 10
    je windows_10_11
    jmp unknown_version
    
check_minor_6:
    mov eax, version_info.dwMinorVersion
    cmp eax, 1
    je windows_7
    cmp eax, 2
    je windows_8
    cmp eax, 3
    je windows_8_1
    jmp unknown_version
    
windows_7:
    invoke lstrcpy, addr info_text, addr "Windows 7 مكتشف"
    jmp show_info
    
windows_8:
    invoke lstrcpy, addr info_text, addr "Windows 8 مكتشف"
    jmp show_info
    
windows_8_1:
    invoke lstrcpy, addr info_text, addr "Windows 8.1 مكتشف"
    jmp show_info
    
windows_10_11:
    invoke lstrcpy, addr info_text, addr "Windows 10/11 مكتشف"
    jmp show_info
    
unknown_version:
    invoke lstrcpy, addr info_text, addr "إصدار Windows غير معروف"
    
show_info:
    invoke MessageBox, NULL, addr info_text, addr title_msg, MB_OK or MB_ICONINFORMATION
    
skip_version_info:
    mov esp, ebp
    pop ebp
    ret
get_system_info endp

start:
    ; رسالة البداية
    invoke MessageBox, NULL, offset test_start, offset title_msg, MB_OK or MB_ICONINFORMATION
    
    ; عرض معلومات النظام
    invoke get_system_info
    
    ; اختبار دعم التشفير
    invoke MessageBox, NULL, offset test_crypto, offset title_msg, MB_OK or MB_ICONINFORMATION
    invoke test_crypto_support
    cmp eax, 0
    je crypto_failed
    
    ; جميع الاختبارات نجحت
    invoke MessageBox, NULL, offset test_success, offset title_msg, MB_OK or MB_ICONINFORMATION
    jmp exit_program
    
crypto_failed:
    invoke MessageBox, NULL, offset crypto_fail, offset title_msg, MB_OK or MB_ICONERROR
    jmp exit_program
    
exit_program:
    invoke ExitProcess, 0

end start
