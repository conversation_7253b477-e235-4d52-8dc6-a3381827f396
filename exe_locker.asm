.386
.model flat, stdcall
option casemap:none

include \masm32\include\windows.inc
include \masm32\include\kernel32.inc
include \masm32\include\user32.inc
include \masm32\include\msvcrt.inc
include \masm32\include\advapi32.inc

includelib \masm32\lib\kernel32.lib
includelib \masm32\lib\user32.lib
includelib \masm32\lib\msvcrt.lib
includelib \masm32\lib\advapi32.lib

; تعريفات Windows Crypto API
PROV_RSA_AES            equ 24
CRYPT_VERIFYCONTEXT     equ 0F0000000h
CALG_AES_256            equ 00006610h
CALG_SHA_256            equ 0000800Ch
CRYPT_EXPORTABLE        equ 00000001h
CRYPT_ENCRYPT           equ 00000001h
CRYPT_DECRYPT           equ 00000002h

.data
    ; رسائل النظام
    title_msg       db "🔐 Advanced Executable Locker v2.0", 0
    password_prompt db "أدخل كلمة المرور لتشغيل البرنامج:", 0
    wrong_password  db "كلمة مرور خاطئة! سيتم إغلاق البرنامج.", 0
    access_granted  db "تم قبول كلمة المرور. جاري فك التشفير...", 0
    crypto_error    db "خطأ في نظام التشفير!", 0

    ; بيانات التشفير
    password_hash   db 32 dup(0)          ; SHA-256 hash
    aes_key         db 32 dup(0)          ; AES-256 key
    iv_data         db 16 dup(0)          ; Initialization Vector

    ; بيانات الملف المحمي
    protected_exe_size dd 0
    protected_exe_data db 2000000 dup(0)  ; مساحة أكبر للملف المحمي

    ; متغيرات العمل
    user_input      db 256 dup(0)
    temp_filename   db "temp_protected.exe", 0
    bytes_written   dd 0
    file_handle     dd 0
    process_info    PROCESS_INFORMATION <>
    startup_info    STARTUPINFO <>

    ; متغيرات التشفير
    hCryptProv      dd 0
    hHash           dd 0
    hKey            dd 0
    dwDataLen       dd 0

.code

; دالة إنشاء مفتاح AES من كلمة المرور
create_aes_key proc password_ptr:DWORD
    push ebp
    mov ebp, esp

    ; الحصول على مزود التشفير
    invoke CryptAcquireContext, offset hCryptProv, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT
    cmp eax, 0
    je key_error

    ; إنشاء hash object
    invoke CryptCreateHash, hCryptProv, CALG_SHA_256, 0, 0, offset hHash
    cmp eax, 0
    je key_error

    ; hash كلمة المرور
    invoke lstrlen, password_ptr
    invoke CryptHashData, hHash, password_ptr, eax, 0
    cmp eax, 0
    je key_error

    ; الحصول على hash value
    mov dwDataLen, 32
    invoke CryptGetHashParam, hHash, 2, offset password_hash, offset dwDataLen, 0
    cmp eax, 0
    je key_error

    ; إنشاء مفتاح AES
    invoke CryptDeriveKey, hCryptProv, CALG_AES_256, hHash, CRYPT_EXPORTABLE, offset hKey
    cmp eax, 0
    je key_error

    mov eax, 1  ; نجح
    jmp key_done

key_error:
    xor eax, eax  ; فشل

key_done:
    ; تنظيف
    cmp hHash, 0
    je skip_hash_cleanup
    invoke CryptDestroyHash, hHash
skip_hash_cleanup:

    mov esp, ebp
    pop ebp
    ret
create_aes_key endp

; دالة تشفير/فك تشفير AES
aes_crypt proc data_ptr:DWORD, data_size:DWORD, encrypt_flag:DWORD
    push ebp
    mov ebp, esp
    local temp_size:DWORD

    mov eax, data_size
    mov temp_size, eax

    ; تشفير أو فك تشفير
    cmp encrypt_flag, CRYPT_ENCRYPT
    je do_encrypt

    ; فك التشفير
    invoke CryptDecrypt, hKey, 0, TRUE, 0, data_ptr, offset temp_size
    jmp crypt_done

do_encrypt:
    ; التشفير
    invoke CryptEncrypt, hKey, 0, TRUE, 0, data_ptr, offset temp_size, data_size

crypt_done:
    cmp eax, 0
    je crypt_error

    mov eax, temp_size  ; إرجاع الحجم الجديد
    jmp crypt_exit

crypt_error:
    xor eax, eax  ; فشل

crypt_exit:
    mov esp, ebp
    pop ebp
    ret
aes_crypt endp

; دالة تنظيف موارد التشفير
cleanup_crypto proc
    cmp hKey, 0
    je skip_key_cleanup
    invoke CryptDestroyKey, hKey
    mov hKey, 0
skip_key_cleanup:

    cmp hCryptProv, 0
    je skip_prov_cleanup
    invoke CryptReleaseContext, hCryptProv, 0
    mov hCryptProv, 0
skip_prov_cleanup:

    ret
cleanup_crypto endp

; دالة التحقق من كلمة المرور
verify_password proc input_ptr:DWORD
    push ebp
    mov ebp, esp
    local input_hash[32]:BYTE
    local temp_prov:DWORD
    local temp_hash:DWORD
    local hash_len:DWORD

    ; إنشاء hash لكلمة المرور المدخلة
    invoke CryptAcquireContext, offset temp_prov, NULL, NULL, PROV_RSA_AES, CRYPT_VERIFYCONTEXT
    cmp eax, 0
    je verify_error

    invoke CryptCreateHash, temp_prov, CALG_SHA_256, 0, 0, offset temp_hash
    cmp eax, 0
    je verify_cleanup

    invoke lstrlen, input_ptr
    invoke CryptHashData, temp_hash, input_ptr, eax, 0
    cmp eax, 0
    je verify_cleanup

    mov hash_len, 32
    invoke CryptGetHashParam, temp_hash, 2, addr input_hash, offset hash_len, 0
    cmp eax, 0
    je verify_cleanup

    ; مقارنة hash values
    mov esi, offset password_hash
    lea edi, input_hash
    mov ecx, 32
    repe cmpsb
    je verify_success

verify_cleanup:
    cmp temp_hash, 0
    je skip_hash_destroy
    invoke CryptDestroyHash, temp_hash
skip_hash_destroy:

    cmp temp_prov, 0
    je verify_error
    invoke CryptReleaseContext, temp_prov, 0

verify_error:
    xor eax, eax  ; فشل
    jmp verify_done

verify_success:
    ; تنظيف
    invoke CryptDestroyHash, temp_hash
    invoke CryptReleaseContext, temp_prov, 0
    mov eax, 1  ; نجح

verify_done:
    mov esp, ebp
    pop ebp
    ret
verify_password endp

; دالة استخراج وتشغيل الملف المحمي
extract_and_run proc
    push ebp
    mov ebp, esp
    local decrypted_size:DWORD

    ; إنشاء مفتاح AES من كلمة المرور
    invoke create_aes_key, offset user_input
    cmp eax, 0
    je extract_crypto_error

    ; فك تشفير بيانات الملف
    invoke aes_crypt, offset protected_exe_data, protected_exe_size, CRYPT_DECRYPT
    cmp eax, 0
    je extract_crypto_error
    mov decrypted_size, eax

    ; إنشاء ملف مؤقت
    invoke CreateFile, offset temp_filename, GENERIC_WRITE, 0, NULL, \
           CREATE_ALWAYS, FILE_ATTRIBUTE_TEMPORARY, NULL
    mov file_handle, eax
    cmp eax, INVALID_HANDLE_VALUE
    je extract_error

    ; كتابة الملف المفكوك
    invoke WriteFile, file_handle, offset protected_exe_data, decrypted_size, \
           offset bytes_written, NULL
    cmp eax, 0
    je extract_error

    ; إغلاق الملف
    invoke CloseHandle, file_handle
    mov file_handle, 0

    ; إعداد معلومات التشغيل
    invoke RtlZeroMemory, offset startup_info, sizeof STARTUPINFO
    mov startup_info.cb, sizeof STARTUPINFO

    ; تشغيل الملف
    invoke CreateProcess, offset temp_filename, NULL, NULL, NULL, FALSE, \
           0, NULL, NULL, offset startup_info, offset process_info
    cmp eax, 0
    je extract_error

    ; انتظار انتهاء العملية
    invoke WaitForSingleObject, process_info.hProcess, INFINITE

    ; تنظيف العمليات
    invoke CloseHandle, process_info.hProcess
    invoke CloseHandle, process_info.hThread

    ; حذف الملف المؤقت
    invoke DeleteFile, offset temp_filename

    ; إعادة تشفير البيانات للأمان
    invoke aes_crypt, offset protected_exe_data, decrypted_size, CRYPT_ENCRYPT

    ; تنظيف موارد التشفير
    invoke cleanup_crypto

    mov esp, ebp
    pop ebp
    ret

extract_crypto_error:
    invoke MessageBox, NULL, offset crypto_error, offset title_msg, MB_OK
    invoke cleanup_crypto
    mov esp, ebp
    pop ebp
    ret

extract_error:
    cmp file_handle, 0
    je skip_file_close
    invoke CloseHandle, file_handle
skip_file_close:
    invoke DeleteFile, offset temp_filename
    invoke MessageBox, NULL, offset wrong_password, offset title_msg, MB_OK
    invoke cleanup_crypto
    mov esp, ebp
    pop ebp
    ret
extract_and_run endp

; دالة إظهار نافذة كلمة المرور المبسطة
show_password_input proc
    local input_buffer[256]:BYTE

    ; استخدام InputBox بسيط (يمكن تحسينه لاحقاً)
    ; للآن سنستخدم محاكاة بسيطة

    ; نسخ كلمة مرور افتراضية للاختبار
    mov esi, offset user_input
    mov byte ptr [esi], 't'
    mov byte ptr [esi+1], 'e'
    mov byte ptr [esi+2], 's'
    mov byte ptr [esi+3], 't'
    mov byte ptr [esi+4], 0

    ret
show_password_input endp

start:
    ; طلب كلمة المرور من المستخدم
    invoke MessageBox, NULL, offset password_prompt, offset title_msg, MB_OK

    ; إظهار نافذة إدخال كلمة المرور
    invoke show_password_input
    
    ; التحقق من كلمة المرور
    invoke verify_password, offset user_input
    cmp eax, 0
    jne password_correct
    
    ; كلمة مرور خاطئة
    invoke MessageBox, NULL, offset wrong_password, offset title_msg, MB_OK
    jmp exit_program
    
password_correct:
    ; كلمة مرور صحيحة
    invoke MessageBox, NULL, offset access_granted, offset title_msg, MB_OK
    
    ; استخراج وتشغيل الملف المحمي
    invoke extract_and_run
    
exit_program:
    invoke ExitProcess, 0

end start
