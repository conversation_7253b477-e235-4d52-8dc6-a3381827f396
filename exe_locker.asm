.386
.model flat, stdcall
option casemap:none

include \masm32\include\windows.inc
include \masm32\include\kernel32.inc
include \masm32\include\user32.inc
include \masm32\include\msvcrt.inc

includelib \masm32\lib\kernel32.lib
includelib \masm32\lib\user32.lib
includelib \masm32\lib\msvcrt.lib

.data
    ; رسائل النظام
    title_msg       db "🔐 Executable File Locker", 0
    password_prompt db "أدخل كلمة المرور لتشغيل البرنامج:", 0
    wrong_password  db "كلمة مرور خاطئة! سيتم إغلاق البرنامج.", 0
    access_granted  db "تم قبول كلمة المرور. جاري تشغيل البرنامج...", 0
    
    ; كلمة المرور المشفرة (سيتم تعديلها لاحقاً)
    encrypted_password db "ENCRYPTED_PASSWORD_PLACEHOLDER", 0
    
    ; بيانات الملف المحمي (سيتم إدراجها لاحقاً)
    protected_exe_size dd 0
    protected_exe_data db 1000000 dup(0)  ; مساحة للملف المحمي
    
    ; متغيرات العمل
    user_input      db 256 dup(0)
    temp_filename   db "temp_protected.exe", 0
    bytes_written   dd 0
    file_handle     dd 0
    process_info    PROCESS_INFORMATION <>
    startup_info    STARTUPINFO <>

.code

; دالة تشفير/فك تشفير XOR بسيط
xor_encrypt proc password_ptr:DWORD, data_ptr:DWORD, data_size:DWORD
    push esi
    push edi
    push ecx
    push edx
    
    mov esi, password_ptr
    mov edi, data_ptr
    mov ecx, data_size
    xor edx, edx
    
encrypt_loop:
    cmp ecx, 0
    je encrypt_done
    
    ; احصل على حرف من كلمة المرور
    mov al, byte ptr [esi + edx]
    cmp al, 0
    jne use_char
    xor edx, edx  ; ابدأ من جديد إذا انتهت كلمة المرور
    mov al, byte ptr [esi]
    
use_char:
    ; قم بعملية XOR
    xor byte ptr [edi], al
    inc edi
    inc edx
    dec ecx
    jmp encrypt_loop
    
encrypt_done:
    pop edx
    pop ecx
    pop edi
    pop esi
    ret
xor_encrypt endp

; دالة التحقق من كلمة المرور
verify_password proc input_ptr:DWORD
    push esi
    push edi
    push ecx
    
    ; فك تشفير كلمة المرور المخزنة
    invoke xor_encrypt, input_ptr, offset encrypted_password, 32
    
    ; مقارنة كلمة المرور
    invoke lstrcmp, input_ptr, offset encrypted_password
    
    ; إعادة تشفير كلمة المرور
    invoke xor_encrypt, input_ptr, offset encrypted_password, 32
    
    pop ecx
    pop edi
    pop esi
    ret
verify_password endp

; دالة استخراج وتشغيل الملف المحمي
extract_and_run proc
    push ebp
    mov ebp, esp
    
    ; إنشاء ملف مؤقت
    invoke CreateFile, offset temp_filename, GENERIC_WRITE, 0, NULL, \
           CREATE_ALWAYS, FILE_ATTRIBUTE_TEMPORARY, NULL
    mov file_handle, eax
    cmp eax, INVALID_HANDLE_VALUE
    je extract_error
    
    ; فك تشفير بيانات الملف
    invoke xor_encrypt, offset user_input, offset protected_exe_data, protected_exe_size
    
    ; كتابة الملف المفكوك
    invoke WriteFile, file_handle, offset protected_exe_data, protected_exe_size, \
           offset bytes_written, NULL
    
    ; إغلاق الملف
    invoke CloseHandle, file_handle
    
    ; إعداد معلومات التشغيل
    mov startup_info.cb, sizeof STARTUPINFO
    
    ; تشغيل الملف
    invoke CreateProcess, offset temp_filename, NULL, NULL, NULL, FALSE, \
           0, NULL, NULL, offset startup_info, offset process_info
    
    cmp eax, 0
    je extract_error
    
    ; انتظار انتهاء العملية
    invoke WaitForSingleObject, process_info.hProcess, INFINITE
    
    ; تنظيف
    invoke CloseHandle, process_info.hProcess
    invoke CloseHandle, process_info.hThread
    
    ; حذف الملف المؤقت
    invoke DeleteFile, offset temp_filename
    
    ; إعادة تشفير البيانات
    invoke xor_encrypt, offset user_input, offset protected_exe_data, protected_exe_size
    
    mov esp, ebp
    pop ebp
    ret
    
extract_error:
    invoke MessageBox, NULL, offset wrong_password, offset title_msg, MB_OK
    mov esp, ebp
    pop ebp
    ret
extract_and_run endp

; دالة إظهار نافذة كلمة المرور المبسطة
show_password_input proc
    local input_buffer[256]:BYTE

    ; استخدام InputBox بسيط (يمكن تحسينه لاحقاً)
    ; للآن سنستخدم محاكاة بسيطة

    ; نسخ كلمة مرور افتراضية للاختبار
    mov esi, offset user_input
    mov byte ptr [esi], 't'
    mov byte ptr [esi+1], 'e'
    mov byte ptr [esi+2], 's'
    mov byte ptr [esi+3], 't'
    mov byte ptr [esi+4], 0

    ret
show_password_input endp

start:
    ; طلب كلمة المرور من المستخدم
    invoke MessageBox, NULL, offset password_prompt, offset title_msg, MB_OK

    ; إظهار نافذة إدخال كلمة المرور
    invoke show_password_input
    
    ; التحقق من كلمة المرور
    invoke verify_password, offset user_input
    cmp eax, 0
    jne password_correct
    
    ; كلمة مرور خاطئة
    invoke MessageBox, NULL, offset wrong_password, offset title_msg, MB_OK
    jmp exit_program
    
password_correct:
    ; كلمة مرور صحيحة
    invoke MessageBox, NULL, offset access_granted, offset title_msg, MB_OK
    
    ; استخراج وتشغيل الملف المحمي
    invoke extract_and_run
    
exit_program:
    invoke ExitProcess, 0

end start
