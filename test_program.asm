.386
.model flat, stdcall
option casemap:none

include \masm32\include\windows.inc
include \masm32\include\kernel32.inc
include \masm32\include\user32.inc

includelib \masm32\lib\kernel32.lib
includelib \masm32\lib\user32.lib

.data
    title_msg   db "🎯 برنامج اختبار", 0
    hello_msg   db "مرحباً! هذا برنامج اختبار للتأكد من عمل نظام الحماية.", 13, 10, 13, 10, \
                   "إذا رأيت هذه الرسالة، فهذا يعني أن:", 13, 10, \
                   "✅ كلمة المرور صحيحة", 13, 10, \
                   "✅ تم فك التشفير بنجاح", 13, 10, \
                   "✅ النظام يعمل بشكل صحيح", 13, 10, 13, 10, \
                   "شكراً لاستخدام نظام حماية الملفات التنفيذية!", 0

.code
start:
    ; عرض رسالة الترحيب
    invoke MessageBox, NULL, offset hello_msg, offset title_msg, MB_OK or MB_ICONINFORMATION
    
    ; إنهاء البرنامج
    invoke ExitProcess, 0

end start
