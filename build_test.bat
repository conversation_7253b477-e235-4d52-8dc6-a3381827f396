@echo off
echo 🎯 Building Test Program...
echo.

REM تحقق من وجود MASM32
if not exist "C:\masm32\bin\ml.exe" (
    echo خطأ: MASM32 غير مثبت أو غير موجود في C:\masm32
    pause
    exit /b 1
)

REM إعداد المتغيرات
set MASM32_PATH=C:\masm32
set PATH=%MASM32_PATH%\bin;%PATH%
set INCLUDE=%MASM32_PATH%\include
set LIB=%MASM32_PATH%\lib

echo تجميع برنامج الاختبار...
ml /c /coff test_program.asm
if errorlevel 1 goto error

echo ربط برنامج الاختبار...
link /SUBSYSTEM:WINDOWS test_program.obj
if errorlevel 1 goto error

echo.
echo ✅ تم تجميع برنامج الاختبار بنجاح!
echo.
echo الملف المنتج: test_program.exe
echo.

REM تنظيف الملفات المؤقتة
del test_program.obj 2>nul

echo يمكنك الآن استخدام test_program.exe لاختبار نظام الحماية
echo.
pause
goto end

:error
echo.
echo ❌ حدث خطأ أثناء تجميع برنامج الاختبار!
pause

:end
