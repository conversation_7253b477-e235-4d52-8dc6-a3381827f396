.386
.model flat, stdcall
option casemap:none

include \masm32\include\windows.inc
include \masm32\include\user32.inc
include \masm32\include\kernel32.inc
include \masm32\include\comctl32.inc

includelib \masm32\lib\user32.lib
includelib \masm32\lib\kernel32.lib
includelib \masm32\lib\comctl32.lib

.const
    ; معرفات العناصر
    ID_EDIT_PASSWORD    equ 1001
    ID_BTN_OK          equ 1002
    ID_BTN_CANCEL      equ 1003
    ID_STATIC_LABEL    equ 1004

.data
    ; معلومات النافذة
    class_name      db "PasswordDialog", 0
    window_title    db "🔐 كلمة المرور مطلوبة", 0
    password_label  db "أدخل كلمة المرور:", 0
    btn_ok_text     db "موافق", 0
    btn_cancel_text db "إلغاء", 0
    
    ; متغيرات النافذة
    hwnd_main       dd 0
    hwnd_edit       dd 0
    hwnd_btn_ok     dd 0
    hwnd_btn_cancel dd 0
    hwnd_label      dd 0
    
    ; نتيجة كلمة المرور
    password_result db 256 dup(0)
    dialog_result   dd 0  ; 1 = OK, 0 = Cancel
    
    ; خط النص
    hfont           dd 0

.code

; دالة معالجة رسائل النافذة
WindowProc proc hwnd:DWORD, uMsg:DWORD, wParam:DWORD, lParam:DWORD
    
    .if uMsg == WM_CREATE
        ; إنشاء الخط
        invoke CreateFont, 16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE, \
               DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS, \
               DEFAULT_QUALITY, DEFAULT_PITCH, NULL
        mov hfont, eax
        
        ; إنشاء التسمية
        invoke CreateWindowEx, 0, addr "STATIC", addr password_label, \
               WS_VISIBLE or WS_CHILD or SS_CENTER, \
               20, 20, 260, 25, hwnd, ID_STATIC_LABEL, NULL, NULL
        mov hwnd_label, eax
        invoke SendMessage, hwnd_label, WM_SETFONT, hfont, TRUE
        
        ; إنشاء صندوق النص
        invoke CreateWindowEx, WS_EX_CLIENTEDGE, addr "EDIT", NULL, \
               WS_VISIBLE or WS_CHILD or WS_BORDER or ES_PASSWORD or ES_AUTOHSCROLL, \
               20, 55, 260, 25, hwnd, ID_EDIT_PASSWORD, NULL, NULL
        mov hwnd_edit, eax
        invoke SendMessage, hwnd_edit, WM_SETFONT, hfont, TRUE
        invoke SetFocus, hwnd_edit
        
        ; إنشاء زر موافق
        invoke CreateWindowEx, 0, addr "BUTTON", addr btn_ok_text, \
               WS_VISIBLE or WS_CHILD or BS_DEFPUSHBUTTON, \
               70, 100, 80, 30, hwnd, ID_BTN_OK, NULL, NULL
        mov hwnd_btn_ok, eax
        invoke SendMessage, hwnd_btn_ok, WM_SETFONT, hfont, TRUE
        
        ; إنشاء زر إلغاء
        invoke CreateWindowEx, 0, addr "BUTTON", addr btn_cancel_text, \
               WS_VISIBLE or WS_CHILD or BS_PUSHBUTTON, \
               160, 100, 80, 30, hwnd, ID_BTN_CANCEL, NULL, NULL
        mov hwnd_btn_cancel, eax
        invoke SendMessage, hwnd_btn_cancel, WM_SETFONT, hfont, TRUE
        
    .elseif uMsg == WM_COMMAND
        mov eax, wParam
        and eax, 0FFFFh
        
        .if eax == ID_BTN_OK
            ; الحصول على كلمة المرور
            invoke GetWindowText, hwnd_edit, addr password_result, 256
            mov dialog_result, 1
            invoke DestroyWindow, hwnd
            
        .elseif eax == ID_BTN_CANCEL
            mov dialog_result, 0
            invoke DestroyWindow, hwnd
            
        .endif
        
    .elseif uMsg == WM_KEYDOWN
        .if wParam == VK_RETURN
            ; Enter = موافق
            invoke GetWindowText, hwnd_edit, addr password_result, 256
            mov dialog_result, 1
            invoke DestroyWindow, hwnd
            
        .elseif wParam == VK_ESCAPE
            ; Escape = إلغاء
            mov dialog_result, 0
            invoke DestroyWindow, hwnd
            
        .endif
        
    .elseif uMsg == WM_CLOSE
        mov dialog_result, 0
        invoke DestroyWindow, hwnd
        
    .elseif uMsg == WM_DESTROY
        .if hfont != 0
            invoke DeleteObject, hfont
        .endif
        invoke PostQuitMessage, 0
        
    .else
        invoke DefWindowProc, hwnd, uMsg, wParam, lParam
        ret
        
    .endif
    
    xor eax, eax
    ret
WindowProc endp

; دالة إظهار نافذة كلمة المرور
ShowPasswordDialog proc
    local wc:WNDCLASSEX
    local msg:MSG
    local hwnd:DWORD
    
    ; تسجيل فئة النافذة
    mov wc.cbSize, sizeof WNDCLASSEX
    mov wc.style, CS_HREDRAW or CS_VREDRAW
    mov wc.lpfnWndProc, offset WindowProc
    mov wc.cbClsExtra, 0
    mov wc.cbWndExtra, 0
    push NULL
    pop wc.hInstance
    invoke LoadIcon, NULL, IDI_APPLICATION
    mov wc.hIcon, eax
    invoke LoadCursor, NULL, IDC_ARROW
    mov wc.hCursor, eax
    mov wc.hbrBackground, COLOR_BTNFACE + 1
    mov wc.lpszMenuName, NULL
    mov wc.lpszClassName, offset class_name
    mov wc.hIconSm, 0
    
    invoke RegisterClassEx, addr wc
    
    ; إنشاء النافذة
    invoke CreateWindowEx, WS_EX_DLGMODALFRAME or WS_EX_TOPMOST, \
           addr class_name, addr window_title, \
           WS_CAPTION or WS_SYSMENU or WS_VISIBLE, \
           CW_USEDEFAULT, CW_USEDEFAULT, 320, 180, \
           NULL, NULL, NULL, NULL
    mov hwnd, eax
    mov hwnd_main, eax
    
    ; حلقة الرسائل
    .while TRUE
        invoke GetMessage, addr msg, NULL, 0, 0
        .break .if eax == 0
        
        invoke TranslateMessage, addr msg
        invoke DispatchMessage, addr msg
    .endw
    
    ; إرجاع النتيجة
    mov eax, dialog_result
    ret
ShowPasswordDialog endp

; دالة الحصول على كلمة المرور المدخلة
GetPasswordResult proc buffer_ptr:DWORD, buffer_size:DWORD
    invoke lstrcpyn, buffer_ptr, addr password_result, buffer_size
    ret
GetPasswordResult endp

; نقطة البداية للاختبار
start:
    invoke ShowPasswordDialog
    
    .if eax == 1
        ; تم إدخال كلمة مرور
        invoke MessageBox, NULL, addr password_result, addr window_title, MB_OK
    .else
        ; تم الإلغاء
        invoke MessageBox, NULL, addr "تم إلغاء العملية", addr window_title, MB_OK
    .endif
    
    invoke ExitProcess, 0

end start
