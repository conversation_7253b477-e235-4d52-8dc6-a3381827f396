@echo off
echo 🔍 Building Compatibility Test...
echo.

REM تحقق من وجود MASM32
if not exist "C:\masm32\bin\ml.exe" (
    echo خطأ: MASM32 غير مثبت أو غير موجود في C:\masm32
    pause
    exit /b 1
)

REM إعداد المتغيرات
set MASM32_PATH=C:\masm32
set PATH=%MASM32_PATH%\bin;%PATH%
set INCLUDE=%MASM32_PATH%\include
set LIB=%MASM32_PATH%\lib

echo تجميع اختبار التوافق...
ml /c /coff /Cp compatibility_test.asm
if errorlevel 1 goto error

echo ربط اختبار التوافق...
link /SUBSYSTEM:WINDOWS /LARGEADDRESSAWARE compatibility_test.obj
if errorlevel 1 goto error

echo.
echo ✅ تم تجميع اختبار التوافق بنجاح!
echo.
echo الملف المنتج: compatibility_test.exe
echo.

REM تنظيف الملفات المؤقتة
del compatibility_test.obj 2>nul

echo 🚀 تشغيل اختبار التوافق...
echo.
compatibility_test.exe

echo.
echo اختبار التوافق مكتمل!
pause
goto end

:error
echo.
echo ❌ حدث خطأ أثناء تجميع اختبار التوافق!
pause

:end
